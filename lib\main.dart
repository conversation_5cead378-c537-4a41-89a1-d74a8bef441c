import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart' show kDebugMode, kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:untitled10/share_borrow_features_screen.dart';
import 'models/story_model.dart';
import 'package:image_picker/image_picker.dart';
import 'package:untitled10/id20.dart';
import 'package:untitled10/id19.dart';
import 'package:animated_background/animated_background.dart';
import 'package:geolocator/geolocator.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:video_player/video_player.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:untitled10/id18.dart';
import 'package:untitled10/id17.dart';
import 'package:untitled10/id16.dart';
import 'package:untitled10/id27.dart';
import 'package:untitled10/id15.dart';
import 'package:untitled10/id14.dart';
import 'package:untitled10/id13.dart';
import 'package:untitled10/id12.dart';
import 'package:untitled10/id11.dart';
import 'package:untitled10/id10.dart';
import 'package:untitled10/id9.dart';
import 'package:untitled10/id8.dart';
import 'package:untitled10/id7.dart';
import 'package:untitled10/id6.dart';
import 'package:untitled10/id5.dart';
import 'package:untitled10/id4.dart';
import 'package:untitled10/id3.dart';
import 'package:untitled10/id2.dart';
import 'package:untitled10/id1.dart';
import 'package:untitled10/id22.dart';
import 'package:untitled10/id23.dart';
import 'package:untitled10/id24.dart';
import 'package:untitled10/id25.dart';
import 'package:untitled10/id26.dart';
import 'providers/reel_provider.dart';
import 'app_integration_setup.dart';
import 'package:untitled10/elmhn.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:lottie/lottie.dart';
import 'package:animate_do/animate_do.dart';
import 'package:shimmer/shimmer.dart';
import 'package:badges/badges.dart' as badges;
import 'config/env_config.dart';
import 'models/notification_model.dart';
import 'models/story_model.dart';
import 'models/user_model.dart';
import 'appstate.dart';
import 'grop.dart';
import 'ecommerce_app.dart' as ecommerce_app;
import 'monthly_meals_screen.dart' as meals;
import 'poll_page.dart' as poll_page;
import 'religious.dart' as religious;
import 'cards_app.dart' as cards_app;
import 'elsaf7aelsha3sya.dart' as profile_lib;
import 'messenger.dart';
import 'video.dart';
import 'SearchPage.dart';
import 'MapPage.dart';
import 'AdminPanel.dart';
import 'services/notification_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'services/connectivity_manager.dart';
import 'notifications_page.dart';
import 'package:untitled10/id21.dart';

// Initialize flutter_local_notifications
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

Future<void> main() async {
  timeago.setLocaleMessages('ar', timeago.ArMessages());
  WidgetsFlutterBinding.ensureInitialized();
  tz.initializeTimeZones();
  if (kDebugMode) HttpOverrides.global = MyHttpOverrides();

  await EnvConfig().initialize();
  debugPrint('🔧 بيئة التطبيق: ${EnvConfig().environment}');
  debugPrint('🔧 عنوان API: ${EnvConfig().apiUrl}');

  await Supabase.initialize(
    url: EnvConfig().supabaseUrl,
    anonKey: EnvConfig().supabaseAnonKey,
  );

  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/ic_launcher');
  const DarwinInitializationSettings initializationSettingsIOS =
      DarwinInitializationSettings(
    requestAlertPermission: true,
    requestBadgePermission: true,
    requestSoundPermission: true,
  );
  const InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsIOS,
  );
  await flutterLocalNotificationsPlugin.initialize(initializationSettings);

  await NotificationService().initialize();
  ConnectivityManager().initialize();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AppState()),
        ChangeNotifierProvider(
            create: (context) => profile_lib.ProfileProvider()),
        ChangeNotifierProvider(create: (context) => XFeedProvider()),
        ChangeNotifierProvider(create: (context) => ReelProvider()),
      ],
      child: Consumer<AppState>(
        builder: (context, appState, _) {
          final profileProvider =
              Provider.of<profile_lib.ProfileProvider>(context, listen: false);
          appState.setProfileProvider(profileProvider);
          return const MyApp();
        },
      ),
    ),
  );
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  static final Map<String, Map<String, dynamic>> themes = {
    'Default': {
      'themeData': ThemeData(
        brightness: Brightness.light,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.teal,
          primary: Colors.teal[700],
          secondary: Colors.amber[600],
          background: Colors.grey[200],
        ),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          elevation: 0,
          backgroundColor: Colors.teal,
          titleTextStyle: TextStyle(
              color: Colors.white,
              fontSize: 22,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal'),
          iconTheme: IconThemeData(color: Colors.white),
        ),
        cardTheme: CardThemeData(
          elevation: 8,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          shadowColor: Colors.black26,
        ),
        textTheme: const TextTheme(
          bodyMedium: TextStyle(fontFamily: 'Tajawal'),
          headlineSmall: TextStyle(fontFamily: 'Tajawal'),
          bodySmall: TextStyle(fontFamily: 'Tajawal'),
        ),
      ),
      'isDark': false,
    },
    'Kids': {
      'themeData': ThemeData(
        brightness: Brightness.light,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.orange,
          background: Colors.yellow[50],
        ),
        useMaterial3: true,
        cardTheme: CardThemeData(
          elevation: 3,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.orange,
          titleTextStyle: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontFamily: 'Tajawal',
          ),
        ),
        textTheme: const TextTheme(
          bodyMedium: TextStyle(fontFamily: 'Tajawal', color: Colors.black87),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
        ),
      ),
      'isDark': false,
    },
    'Men': {
      'themeData': ThemeData(
        brightness: Brightness.dark,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.grey,
          brightness: Brightness.dark,
          background: Colors.grey[900],
        ),
        useMaterial3: true,
        cardTheme: CardThemeData(
          elevation: 4,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.black,
          titleTextStyle: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal'),
        ),
        textTheme: const TextTheme(
            bodyMedium:
                TextStyle(color: Colors.white70, fontFamily: 'Tajawal')),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
        ),
      ),
      'isDark': true,
    },
    'Women': {
      'themeData': ThemeData(
        brightness: Brightness.light,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.pink,
          background: Colors.pink[50],
        ),
        useMaterial3: true,
        cardTheme: CardThemeData(
          elevation: 4,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.pink,
          titleTextStyle: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal'),
        ),
        textTheme: const TextTheme(
            bodyMedium:
                TextStyle(color: Colors.black87, fontFamily: 'Tajawal')),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(backgroundColor: Colors.pink),
        ),
      ),
      'isDark': false,
    },
    'Dark Fantasy': {
      'themeData': ThemeData(
        brightness: Brightness.dark,
        primarySwatch: Colors.purple,
        scaffoldBackgroundColor: Colors.black,
        cardTheme: CardThemeData(
          elevation: 8,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          color: Colors.purpleAccent,
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.purple,
          titleTextStyle: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal'),
        ),
        textTheme: const TextTheme(
            bodyMedium: TextStyle(color: Colors.white, fontFamily: 'Tajawal')),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
        ),
      ),
      'isDark': true,
    },
    'Nature': {
      'themeData': ThemeData(
        brightness: Brightness.light,
        primarySwatch: Colors.green,
        scaffoldBackgroundColor: Colors.green[50],
        cardTheme: CardThemeData(
          elevation: 5,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.green,
          titleTextStyle: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal'),
        ),
        textTheme: const TextTheme(
            bodyMedium: TextStyle(color: Colors.green, fontFamily: 'Tajawal')),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
        ),
      ),
      'isDark': false,
    },
    'Ocean': {
      'themeData': ThemeData(
        brightness: Brightness.light,
        primarySwatch: Colors.teal,
        scaffoldBackgroundColor: Colors.teal[50],
        cardTheme: CardThemeData(
          elevation: 5,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.teal,
          titleTextStyle: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal'),
        ),
        textTheme: const TextTheme(
            bodyMedium: TextStyle(color: Colors.teal, fontFamily: 'Tajawal')),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
        ),
      ),
      'isDark': false,
    },
    'Retro': {
      'themeData': ThemeData(
        brightness: Brightness.light,
        primarySwatch: Colors.brown,
        scaffoldBackgroundColor: Colors.brown[50],
        cardTheme: CardThemeData(
          elevation: 5,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.brown,
          titleTextStyle: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal'),
        ),
        textTheme: const TextTheme(
            bodyMedium: TextStyle(color: Colors.brown, fontFamily: 'Tajawal')),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(backgroundColor: Colors.brown),
        ),
      ),
      'isDark': false,
    },
    'Minimal': {
      'themeData': ThemeData(
        brightness: Brightness.light,
        primarySwatch: Colors.grey,
        scaffoldBackgroundColor: Colors.white,
        cardTheme: CardThemeData(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.grey,
          titleTextStyle: TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal'),
        ),
        textTheme: const TextTheme(
            bodyMedium: TextStyle(color: Colors.black, fontFamily: 'Tajawal')),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
        ),
      ),
      'isDark': false,
    },
  };

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return MaterialApp(
          title: 'Fulk',
          debugShowCheckedModeBanner: false,
          scaffoldMessengerKey: appState.scaffoldMessengerKey,
          initialRoute: '/',
          routes: {
            '/': (context) =>
                appState.isLoggedIn ? const MyHomePage() : const LoginPage(),
            '/ecommerce': (context) => const ecommerce_app.EcommerceApp(),
            '/monthly_meals': (context) => const meals.MonthlyMealsScreen(),
            '/search': (context) => const SearchPage(),
            '/map': (context) => const MapPage(),
            '/admin': (context) => const AdminPanel(),
            '/religious': (context) => const religious.ReligiousHomePage(),
            '/polls': (context) => const poll_page.PollPage(),
            '/cards': (context) => const cards_app.CardsApp(),
            '/video': (context) => const RelatedVideosScreen(),
            '/messenger': (context) => const Messenger(),
            '/Grop': (context) => const Grop(),
            '/profile': (context) => const profile_lib.IconGridScreen(),
            '/elmhn': (context) =>
                const CourseDetailsPage1(courseTitle: 'Default Course'),
            '/notifications': (context) =>
                NotificationsScreen(notifications: appState.notifications),
          },
          theme: themes[appState.selectedTheme]?['themeData'] ??
              themes['Default']!['themeData'],
          darkTheme: ThemeData(
            brightness: Brightness.dark,
            primarySwatch: Colors.teal,
            useMaterial3: true,
            textTheme: const TextTheme(
              bodyMedium: TextStyle(fontFamily: 'Tajawal'),
              headlineSmall: TextStyle(fontFamily: 'Tajawal'),
              bodySmall: TextStyle(fontFamily: 'Tajawal'),
            ),
          ),
          themeMode: appState.isDarkMode ? ThemeMode.dark : ThemeMode.light,
          builder: (context, child) => Directionality(
            textDirection: TextDirection.rtl,
            child: child!,
          ),
        );
      },
    );
  }
}

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  String? username, password, phoneNumber, job;
  String userType = UserRole.member.name;
  XFile? idCardImage;
  String? location;
  bool _isLogin = true;
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _isPhoneVerified = false;
  bool _isVerifying = false;
  String _verificationCode = '';
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final String _twilioVerifyServiceSid = 'VAea6a7d36fe51831ea7da2406a10a9942';

  final List<String> jobs = [
    'مهندس',
    'مدرس',
    'موظف حكومي',
    'محاسب',
    'مبرمج',
    'سائق',
    'عامل حر (فريلانسر)',
    'طالب',
    'صاحب عمل/تاجر',
    'أخرى',
  ];

  final List<String> medicalJobs = [
    'مستشفى',
    'مركز أشعة',
    'معمل تحاليل',
    'عيادة خاصة',
    'صيدلية',
    'مركز علاج طبيعي',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    );
    _animationController.forward();
    _checkLocationPermission();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }
    if (permission == LocationPermission.whileInUse ||
        permission == LocationPermission.always) {
      try {
        Position position = await Geolocator.getCurrentPosition();
        setState(() {
          location = '${position.latitude},${position.longitude}';
        });
      } catch (e) {
        if (kDebugMode) print('Error getting location: $e');
      }
    }
  }

  Future<void> _pickIdCardImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        idCardImage = pickedFile;
      });
    }
  }

  Future<void> _sendVerificationCode() async {
    if (phoneNumber == null || phoneNumber!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال رقم هاتف صحيح')),
      );
      return;
    }

    setState(() => _isVerifying = true);

    try {
      String formattedPhoneNumber = phoneNumber!;
      if (!formattedPhoneNumber.startsWith('+')) {
        if (formattedPhoneNumber.startsWith('0')) {
          formattedPhoneNumber = '+2${formattedPhoneNumber.substring(1)}';
        } else {
          formattedPhoneNumber = '+2$formattedPhoneNumber';
        }
      }

      final response = await http.post(
        Uri.parse('${AppState.getBackendUrl()}/api/auth/send-verification'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'phoneNumber': formattedPhoneNumber,
          'serviceSid': _twilioVerifyServiceSid,
          'channel': 'sms'
        }),
      );

      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إرسال رمز التحقق إلى رقم هاتفك')),
        );
      } else {
        final errorData = jsonDecode(response.body);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  'فشل إرسال رمز التحقق: ${errorData['message'] ?? "حدث خطأ"}')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: $e')),
      );
    } finally {
      setState(() => _isVerifying = false);
    }
  }

  Future<void> _verifyCode() async {
    if (_verificationCode.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال رمز التحقق')),
      );
      return;
    }

    setState(() => _isVerifying = true);

    try {
      String formattedPhoneNumber = phoneNumber!;
      if (!formattedPhoneNumber.startsWith('+')) {
        if (formattedPhoneNumber.startsWith('0')) {
          formattedPhoneNumber = '+2${formattedPhoneNumber.substring(1)}';
        } else {
          formattedPhoneNumber = '+2$formattedPhoneNumber';
        }
      }

      final response = await http.post(
        Uri.parse('${AppState.getBackendUrl()}/api/auth/verify-code'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'phoneNumber': formattedPhoneNumber,
          'code': _verificationCode,
          'serviceSid': _twilioVerifyServiceSid
        }),
      );

      if (response.statusCode == 200) {
        setState(() => _isPhoneVerified = true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم التحقق من رقم هاتفك بنجاح')),
        );
      } else {
        final errorData = jsonDecode(response.body);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  'فشل التحقق من الرمز: ${errorData['message'] ?? "رمز غير صحيح"}')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: $e')),
      );
    } finally {
      setState(() => _isVerifying = false);
    }
  }

  Future<void> _submitForm(AppState appState) async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      if (!_isLogin && !_isPhoneVerified) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يرجى التحقق من رقم هاتفك أولاً')),
        );
        return;
      }

      setState(() => _isLoading = true);
      try {
        if (_isLogin) {
          await appState.login(phoneNumber!, password!, userType);
        } else {
          await appState.register(
            username!,
            password!,
            phoneNumber!,
            job!,
            idCardImage?.path,
            location,
            userType,
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[200]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
              child: Form(
                key: _formKey,
                child: ListView(
                  children: [
                    const SizedBox(height: 40),
                    Center(
                      child: Text(
                        _isLogin ? 'تسجيل الدخول' : 'إنشاء حساب',
                        style: const TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    ZoomIn(
                      child: _buildNeumorphicField(
                        child: DropdownButtonFormField<String>(
                          value: userType,
                          decoration: const InputDecoration(
                            labelText: 'نوع المستخدم',
                            border: InputBorder.none,
                            labelStyle: TextStyle(
                                color: Colors.teal, fontFamily: 'Tajawal'),
                          ),
                          items: [
                            UserRole.hokama,
                            UserRole.olama,
                            UserRole.mogtahdin,
                            UserRole.medical,
                            UserRole.member
                          ]
                              .map((role) => DropdownMenuItem(
                                    value: role.name,
                                    child: Text(role.label,
                                        style: const TextStyle(
                                            fontFamily: 'Tajawal')),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              if (value != null) {
                                userType = value;
                                job = null;
                              }
                            });
                          },
                          validator: (value) =>
                              value == null ? 'يرجى اختيار نوع المستخدم' : null,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (!_isLogin)
                      ZoomIn(
                        child: _buildNeumorphicField(
                          child: TextFormField(
                            decoration: const InputDecoration(
                              labelText: 'اسم المستخدم',
                              border: InputBorder.none,
                              prefixIcon:
                                  Icon(Icons.person, color: Colors.teal),
                              labelStyle: TextStyle(fontFamily: 'Tajawal'),
                            ),
                            validator: (value) => value!.isEmpty
                                ? 'يرجى إدخال اسم المستخدم'
                                : null,
                            onSaved: (value) => username = value,
                          ),
                        ),
                      ),
                    if (!_isLogin) const SizedBox(height: 16),
                    ZoomIn(
                      child: _buildNeumorphicField(
                        child: Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                decoration: const InputDecoration(
                                  labelText: 'رقم الهاتف',
                                  border: InputBorder.none,
                                  prefixIcon:
                                      Icon(Icons.phone, color: Colors.teal),
                                  labelStyle: TextStyle(fontFamily: 'Tajawal'),
                                ),
                                keyboardType: TextInputType.phone,
                                validator: (value) => value!.isEmpty
                                    ? 'يرجى إدخال رقم الهاتف'
                                    : null,
                                onSaved: (value) => phoneNumber = value,
                                onChanged: (value) => phoneNumber = value,
                              ),
                            ),
                            if (!_isLogin && !_isPhoneVerified)
                              TextButton(
                                onPressed:
                                    _isVerifying ? null : _sendVerificationCode,
                                child: _isVerifying
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : const Text('تحقق',
                                        style: TextStyle(color: Colors.teal)),
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (!_isLogin && !_isPhoneVerified)
                      ZoomIn(
                        child: _buildNeumorphicField(
                          child: Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  decoration: const InputDecoration(
                                    labelText: 'رمز التحقق',
                                    border: InputBorder.none,
                                    prefixIcon: Icon(Icons.security,
                                        color: Colors.teal),
                                    labelStyle:
                                        TextStyle(fontFamily: 'Tajawal'),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) =>
                                      _verificationCode = value,
                                ),
                              ),
                              TextButton(
                                onPressed: _isVerifying ? null : _verifyCode,
                                child: _isVerifying
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : const Text('تأكيد',
                                        style: TextStyle(color: Colors.teal)),
                              ),
                            ],
                          ),
                        ),
                      ),
                    if (!_isLogin && !_isPhoneVerified)
                      const SizedBox(height: 16),
                    ZoomIn(
                      child: _buildNeumorphicField(
                        child: TextFormField(
                          decoration: InputDecoration(
                            labelText: 'كلمة المرور',
                            border: InputBorder.none,
                            prefixIcon:
                                const Icon(Icons.lock, color: Colors.teal),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _isPasswordVisible
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: Colors.teal,
                              ),
                              onPressed: () {
                                setState(() =>
                                    _isPasswordVisible = !_isPasswordVisible);
                              },
                            ),
                            labelStyle: const TextStyle(fontFamily: 'Tajawal'),
                          ),
                          obscureText: !_isPasswordVisible,
                          validator: (value) => value!.isEmpty
                              ? 'يرجى إدخال كلمة المرور'
                              : value.length < 6
                                  ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
                                  : null,
                          onSaved: (value) => password = value,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (!_isLogin && userType != 'guest')
                      ZoomIn(
                        child: _buildNeumorphicField(
                          child: DropdownButtonFormField<String>(
                            value: job,
                            decoration: const InputDecoration(
                              labelText: 'المهنة',
                              border: InputBorder.none,
                              labelStyle: TextStyle(
                                  color: Colors.teal, fontFamily: 'Tajawal'),
                            ),
                            items: (userType == 'medical' ? medicalJobs : jobs)
                                .map((job) => DropdownMenuItem(
                                    value: job,
                                    child: Text(job,
                                        style: const TextStyle(
                                            fontFamily: 'Tajawal'))))
                                .toList(),
                            onChanged: (value) {
                              setState(() => job = value);
                            },
                            validator: (value) =>
                                value == null ? 'يرجى اختيار المهنة' : null,
                            onSaved: (value) => job = value,
                          ),
                        ),
                      ),
                    if (!_isLogin && userType != 'guest')
                      const SizedBox(height: 16),
                    if (!_isLogin && userType != 'guest')
                      ZoomIn(
                        child: _buildNeumorphicField(
                          child: ListTile(
                            title: Text(
                              idCardImage == null
                                  ? 'اختر صورة الهوية'
                                  : 'تم اختيار صورة الهوية',
                              style: const TextStyle(
                                  color: Colors.teal, fontFamily: 'Tajawal'),
                            ),
                            trailing:
                                const Icon(Icons.image, color: Colors.teal),
                            onTap: _pickIdCardImage,
                          ),
                        ),
                      ),
                    const SizedBox(height: 24),
                    _isLoading
                        ? const Center(
                            child:
                                CircularProgressIndicator(color: Colors.white))
                        : ZoomIn(
                            child: ElevatedButton(
                              onPressed: () => _submitForm(appState),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.teal[700],
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 5,
                              ),
                              child: Text(
                                _isLogin ? 'تسجيل الدخول' : 'إنشاء حساب',
                                style: const TextStyle(
                                    fontSize: 18,
                                    color: Colors.white,
                                    fontFamily: 'Tajawal'),
                              ),
                            ),
                          ),
                    const SizedBox(height: 16),
                    if (_isLogin)
                      ZoomIn(
                        child: TextButton(
                          onPressed: () async {
                            setState(() => _isLoading = true);
                            await appState.loginAsGuest();
                            setState(() => _isLoading = false);
                          },
                          child: const Text(
                            'تسجيل الدخول كزائر',
                            style: TextStyle(
                                color: Colors.white70, fontFamily: 'Tajawal'),
                          ),
                        ),
                      ),
                    if (_isLogin)
                      ZoomIn(
                        child: TextButton(
                          onPressed: () async {
                            setState(() => _isLoading = true);
                            await appState.loginAsHokama();
                            setState(() => _isLoading = false);
                          },
                          child: const Text(
                            'تسجيل الدخول كحكيم (تجريبي)',
                            style: TextStyle(
                                color: Colors.amber,
                                fontFamily: 'Tajawal',
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                    ZoomIn(
                      child: TextButton(
                        onPressed: () {
                          setState(() {
                            _isLogin = !_isLogin;
                            _formKey.currentState?.reset();
                            idCardImage = null;
                            job = null;
                            _isPhoneVerified = false;
                          });
                        },
                        child: Text(
                          _isLogin
                              ? 'ليس لديك حساب؟ إنشاء حساب'
                              : 'لديك حساب؟ تسجيل الدخول',
                          style: const TextStyle(
                              color: Colors.white70, fontFamily: 'Tajawal'),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNeumorphicField({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.teal[50],
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.teal[100]!,
            offset: const Offset(4, 4),
            blurRadius: 8,
          ),
          const BoxShadow(
            color: Colors.white,
            offset: Offset(-4, -4),
            blurRadius: 8,
          ),
        ],
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        width: double.infinity,
        child: child,
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> with TickerProviderStateMixin {
  int _currentTabIndex = 0;
  late AnimationController _animationController;
  late AnimationController _appBarAnimationController;
  late Animation<double> _appBarFadeAnimation;
  final List<int> _favoriteIndices = [];

  final List<Map<String, dynamic>> tabs = [
    {
      'icon': FontAwesomeIcons.houseChimneyWindow,
      'label': 'الرئيسية',
      'screen': null
    },
    {
      'icon': FontAwesomeIcons.solidCommentDots,
      'label': 'الرسائل',
      'screen': const Messenger()
    },
    {
      'icon': FontAwesomeIcons.squarePollVertical,
      'label': 'الاستطلاعات',
      'screen': const poll_page.PollPage()
    },
    {
      'icon': FontAwesomeIcons.bowlFood,
      'label': 'الوجبات الشهرية',
      'screen': const meals.MonthlyMealsScreen()
    },
    {
      'icon': FontAwesomeIcons.film,
      'label': 'الفيديوهات',
      'screen': const RelatedVideosScreen()
    },
    {
      'icon': Icons.group_work,
      'label': 'عالمك المثالي',
      'screen': const cards_app.CardsApp()
    },
    {
      'icon': Icons.person,
      'label': 'الملف الشخصي',
      'screen': const profile_lib.IconGridScreen()
    },
    {
      'icon': FontAwesomeIcons.briefcase,
      'label': 'المهن',
      'screen': const CourseDetailsPage1(courseTitle: 'المهن')
    },
    {
      'icon': FontAwesomeIcons.prayingHands,
      'label': 'تواصل مع اللة',
      'screen': const religious.ReligiousHomePage()
    },
    {
      'icon': Icons.store,
      'label': 'المتجر',
      'screen': const ecommerce_app.EcommerceApp()
    },
    {'icon': Icons.group, 'label': 'مع بعض', 'screen': const Grop()},
    {
      'id': '1',
      'icon': FontAwesomeIcons.bookOpenReader,
      'label': 'أفق المعرفة',
      'screen': const ProfessionsListPage(),
    },
    {
      'id': '2',
      'icon': FontAwesomeIcons.handshakeAngle,
      'label': 'جسور الفرص',
      'screen': const Rayan2(),
    },
    {
      'id': '3',
      'icon': FontAwesomeIcons.lightbulb,
      'label': 'نسيج الأفكار',
      'screen': const Rayan3(),
    },
    {
      'id': '4',
      'icon': Icons.explore,
      'label': 'نور التكافل',
      'screen': const Rayan4(),
    },
    {
      'id': '5',
      'icon': Icons.shield,
      'label': 'ميثاق الود',
      'screen': const Rayan5(),
    },
    {
      'id': '6',
      'icon': Icons.explore,
      'label': 'أثر الضائع',
      'screen': const Rayan6(),
    },
    {
      'id': '7',
      'icon': Icons.history,
      'label': 'سوق الثقة',
      'screen': const Rayan7(),
    },
    {
      'id': '8',
      'icon': Icons.star,
      'label': 'ركن الموهبة',
      'screen': const Rayan8(),
    },
    {
      'id': '9',
      'icon': Icons.explore,
      'label': 'ميدان الابتكار',
      'screen': const Rayan9(),
    },
    {
      'id': '10',
      'icon': Icons.shield,
      'label': 'ملتقى المهارة',
      'screen': const Rayan10(),
    },
    {
      'id': '11',
      'icon': Icons.explore,
      'label': 'وصلة',
      'screen': const Rayan11(),
    },
    {
      'id': '12',
      'icon': Icons.history,
      'label': 'نور الرعاية',
      'screen': const Rayan12(),
    },
    {
      'id': '13',
      'icon': Icons.star,
      'label': 'نبض الطبيعة',
      'screen': const Rayan13(),
    },
    {
      'id': '14',
      'icon': Icons.explore,
      'label': 'أفق الرؤية',
      'screen': const Rayan14(),
    },
    {
      'id': '15',
      'icon': Icons.shield,
      'label': 'فريقي',
      'screen': const Rayan15(),
    },
    {
      'id': '16',
      'icon': FontAwesomeIcons.dove,
      'label': 'عالم تربية الطيور',
      'screen': const BirdBreedingPage(),
    },
    {
      'id': '17',
      'icon': Icons.local_florist,
      'label': 'عالم تربية النبات',
      'screen': const AgriculturalPlantsPage(),
    },
    {
      'id': '18',
      'icon': FontAwesomeIcons.paw,
      'label': 'عالم تربية الحيوان',
      'screen': const AnimalBreedingPage(),
    },
    {
      'id': '19',
      'icon': FontAwesomeIcons.fish,
      'label': 'عالم تربية الأسماك',
      'screen': const FishBreedingPage(),
    },
    {
      'id': '20',
      'icon': FontAwesomeIcons.utensils,
      'label': 'الصناعات الغذائية',
      'screen': const HomeFoodIndustriesPage(),
    },
    {
      'id': '21',
      'icon': Icons.build,
      'label': 'صيانة الأجهزة الكهربائية والإلكترونية',
      'screen': const DeviceMaintenancePage(),
    },
    {
      'id': '22',
      'icon': Icons.share,
      'label': 'بيت المشاركة',
      'screen': const ShareBorrowScreen(),
    },
    {
      'id': '23',
      'icon': FontAwesomeIcons.carSide,
      'label': 'يد العابر',
      'screen': const RideServicePage(),
    },
    {
      'id': '24',
      'icon': FontAwesomeIcons.child,
      'label': 'رحلة الاكتشاف والمعرفة',
      'screen': const ChildCareApp(),
    },
    {
      'id': '25',
      'icon': Icons.group,
      'label': 'نور الأخوة',
      'screen': const GroupsPage(),
    },
    {
      'id': '26',
      'icon': FontAwesomeIcons.clapperboard,
      'label': 'ومضة',
      'screen': ReelsScreen(onToggleTheme: () {
        print("Toggle theme pressed from main.dart");
      }),
    },
    {
      'id': '27',
      'icon': FontAwesomeIcons.clapperboard,
      'label': 'Z',
      'screen': XFeedPage(),
    },
    {
      'id': '28',
      'icon': Icons.settings_applications,
      'label': 'إعدادات التكامل',
      'screen': AppIntegrationSetup.buildSettingsPage(context),
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
        duration: const Duration(milliseconds: 300), vsync: this);
    _appBarAnimationController = AnimationController(
        duration: const Duration(milliseconds: 600), vsync: this);
    _appBarFadeAnimation = CurvedAnimation(
        parent: _appBarAnimationController, curve: Curves.easeInOut);
    tabs[0]['screen'] = HomeScreen(vsync: this);
    _loadFavorites();
    _appBarAnimationController.forward();
  }

  Future<void> _loadFavorites() async {
    // Placeholder for SharedPreferences
  }

  Future<void> _saveFavorites() async {
    // Placeholder for SharedPreferences
  }

  void _toggleFavorite(int index) {
    setState(() {
      if (_favoriteIndices.contains(index)) {
        _favoriteIndices.remove(index);
      } else {
        _favoriteIndices.add(index);
      }
      _saveFavorites();
    });
  }

  bool _isFavorite(int index) => _favoriteIndices.contains(index);

  List<Map<String, dynamic>> get _favoriteItems {
    return _favoriteIndices.map((index) => tabs[index]).toList();
  }

  List<PopupMenuEntry<int>> _buildAdventuresMenuItems() {
    final adventureTabs = tabs.where((tab) => tab.containsKey('id')).toList();
    return [
      const PopupMenuItem(
        enabled: false,
        child: Text(
          'المغامرات',
          style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.teal,
              fontFamily: 'Tajawal'),
        ),
      ),
      ...adventureTabs.asMap().entries.map((entry) {
        final index = tabs.indexOf(entry.value);
        final tab = entry.value;
        return PopupMenuItem<int>(
          value: index,
          child: Row(
            children: [
              Icon(tab['icon'] as IconData,
                  color: _isFavorite(index) ? Colors.amber : Colors.teal),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  tab['label'] as String,
                  style: const TextStyle(fontFamily: 'Tajawal'),
                ),
              ),
              IconButton(
                icon: Icon(
                  _isFavorite(index) ? Icons.star : Icons.star_border,
                  color: _isFavorite(index) ? Colors.amber : Colors.grey,
                  size: 20,
                ),
                onPressed: () {
                  _toggleFavorite(index);
                  Navigator.pop(context);
                  if (mounted) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      showMenu(
                        context: context,
                        position: const RelativeRect.fromLTRB(1000, 100, 0, 0),
                        items: _buildAdventuresMenuItems(),
                      );
                    });
                  }
                },
                splashRadius: 15,
              ),
            ],
          ),
        );
      }),
    ];
  }

  List<PopupMenuEntry<int>> _buildMainMenuItems() {
    final mainTabs = tabs.where((tab) => !tab.containsKey('id')).toList();
    return [
      if (_favoriteIndices.isNotEmpty) ...[
        const PopupMenuDivider(),
        const PopupMenuItem(
          enabled: false,
          child: Text(
            'المفضلة',
            style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.teal,
                fontFamily: 'Tajawal'),
          ),
        ),
        ..._favoriteIndices.map((index) {
          final item = tabs[index];
          return PopupMenuItem<int>(
            value: index,
            child: Row(
              children: [
                Icon(item['icon'] as IconData, color: Colors.amber),
                const SizedBox(width: 8),
                Text(
                  item['label'] as String,
                  style: const TextStyle(fontFamily: 'Tajawal'),
                ),
              ],
            ),
          );
        }),
        const PopupMenuDivider(),
      ],
      const PopupMenuItem(
        enabled: false,
        child: Text(
          'الصفحات الرئيسية',
          style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.teal,
              fontFamily: 'Tajawal'),
        ),
      ),
      ...mainTabs.asMap().entries.map((entry) {
        final index = tabs.indexOf(entry.value);
        final tab = entry.value;
        return PopupMenuItem<int>(
          value: index,
          child: Row(
            children: [
              Icon(tab['icon'] as IconData,
                  color: _isFavorite(index) ? Colors.amber : Colors.teal),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  tab['label'] as String,
                  style: const TextStyle(fontFamily: 'Tajawal'),
                ),
              ),
              IconButton(
                icon: Icon(
                  _isFavorite(index) ? Icons.star : Icons.star_border,
                  color: _isFavorite(index) ? Colors.amber : Colors.grey,
                  size: 20,
                ),
                onPressed: () {
                  _toggleFavorite(index);
                  Navigator.pop(context);
                  if (mounted) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      showMenu(
                        context: context,
                        position: const RelativeRect.fromLTRB(1000, 100, 0, 0),
                        items: _buildMainMenuItems(),
                      );
                    });
                  }
                },
                splashRadius: 15,
              ),
            ],
          ),
        );
      }),
    ];
  }

  Widget _buildFavoritesMenu() {
    if (_favoriteIndices.isEmpty) {
      return PopupMenuButton<String>(
        icon: const Icon(Icons.star_border, color: Colors.white),
        tooltip: 'المفضلة',
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'no_favorites',
            child: Text('لا توجد صفحات مفضلة بعد',
                style: TextStyle(fontFamily: 'Tajawal')),
          ),
        ],
      );
    }

    return PopupMenuButton<int>(
      icon: const Icon(Icons.star, color: Colors.amber),
      tooltip: 'المفضلة',
      onSelected: (index) {
        if (index < tabs.length) {
          final tab = tabs[index];
          final screen = tab['screen'] as Widget?;
          if (screen != null) {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => screen),
            );
          }
        } else {
          setState(() {
            _currentTabIndex = index;
            _animationController.forward(from: 0);
          });
        }
      },
      itemBuilder: (context) => _favoriteIndices.map((index) {
        final item = tabs[index];
        return PopupMenuItem<int>(
          value: index,
          child: Row(
            children: [
              Icon(item['icon'] as IconData, color: Colors.amber),
              const SizedBox(width: 8),
              Text(
                item['label'] as String,
                style: const TextStyle(fontFamily: 'Tajawal'),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.star, color: Colors.amber, size: 20),
                onPressed: () => _toggleFavorite(index),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _appBarAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70.0),
        child: FadeTransition(
          opacity: _appBarFadeAnimation,
          child: AppBar(
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.teal[800]!, Colors.teal[400]!],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
            ),
            title: Row(
              children: [
                ZoomIn(
                  child: Image.asset(
                    'assets/images/logo.png',
                    height: 40,
                    width: 40,
                    errorBuilder: (context, error, stackTrace) =>
                        const Icon(Icons.error, color: Colors.white),
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  'Fulk',
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                      fontFamily: 'Tajawal'),
                ),
              ],
            ),
            actions: [
              IconButton(
                icon: Consumer<AppState>(
                  builder: (context, appState, _) {
                    int unreadCount =
                        appState.notifications.where((n) => !n.isRead).length;
                    return badges.Badge(
                      showBadge: unreadCount > 0,
                      badgeContent: Text(
                        '$unreadCount',
                        style:
                            const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                      badgeStyle: const badges.BadgeStyle(
                        badgeColor: Colors.red,
                        padding: EdgeInsets.all(5),
                      ),
                      child:
                          const Icon(Icons.notifications, color: Colors.white),
                    );
                  },
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const NotificationScreen()),
                  );
                },
                tooltip: 'الإشعارات',
              ),
              _buildFavoritesMenu(),
              PopupMenuButton<int>(
                key: ValueKey('main_menu_${_favoriteIndices.length}'),
                onSelected: (index) {
                  setState(() {
                    _currentTabIndex = index;
                    _animationController.forward(from: 0);
                  });
                },
                itemBuilder: (context) => _buildMainMenuItems(),
                icon: const Icon(FontAwesomeIcons.barsStaggered,
                    color: Colors.white),
                tooltip: 'القائمة الرئيسية',
              ),
              PopupMenuButton<int>(
                key: ValueKey('adventure_menu_${_favoriteIndices.length}'),
                onSelected: (index) {
                  setState(() {
                    _currentTabIndex = index;
                    _animationController.forward(from: 0);
                  });
                },
                itemBuilder: (context) => _buildAdventuresMenuItems(),
                icon: const Icon(FontAwesomeIcons.compass, color: Colors.white),
                tooltip: 'عالمك المثالي',
              ),
              PopupMenuButton<String>(
                onSelected: (theme) {
                  appState.setTheme(theme);
                },
                itemBuilder: (context) => MyApp.themes.keys
                    .map((theme) => PopupMenuItem(
                          value: theme,
                          child: Text(
                            theme,
                            style: const TextStyle(fontFamily: 'Tajawal'),
                          ),
                        ))
                    .toList(),
                icon: const Icon(Icons.palette, color: Colors.white),
                tooltip: 'الثيمات',
              ),
              IconButton(
                icon: const Icon(Icons.logout, color: Colors.white),
                onPressed: () async {
                  await appState.logout();
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => const LoginPage()),
                  );
                },
                tooltip: 'تسجيل الخروج',
              ),
            ],
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(20),
              ),
            ),
          ),
        ),
      ),
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (child, animation) => FadeTransition(
          opacity: animation,
          child: child,
        ),
        child: tabs[_currentTabIndex]['screen'] as Widget,
      ),
    );
  }
}

class NotificationsScreen extends StatelessWidget {
  final List<NotificationModel> notifications;

  const NotificationsScreen({super.key, required this.notifications});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات', style: TextStyle(fontFamily: 'Tajawal')),
      ),
      body: notifications.isEmpty
          ? const Center(
              child: Text('لا توجد إشعارات حاليًا',
                  style: TextStyle(fontFamily: 'Tajawal')))
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                return FadeInDown(
                  duration: const Duration(milliseconds: 300),
                  child: Card(
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    child: ListTile(
                      leading: Icon(
                        notification.isRead
                            ? Icons.notifications
                            : Icons.notifications_active,
                        color: notification.isRead
                            ? Colors.grey
                            : Theme.of(context).primaryColor,
                      ),
                      title: Text(notification.message,
                          style: const TextStyle(fontFamily: 'Tajawal')),
                      subtitle: Text(notification.date,
                          style: const TextStyle(fontFamily: 'Tajawal')),
                      trailing: Text(
                        notification.date,
                        style: Theme.of(context)
                            .textTheme
                            .bodySmall
                            ?.copyWith(fontFamily: 'Tajawal'),
                      ),
                      onTap: () {},
                    ),
                  ),
                );
              },
            ),
    );
  }
}

class HomeScreen extends StatefulWidget {
  final TickerProvider vsync;

  const HomeScreen({super.key, required this.vsync});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final ScrollController _scrollController = ScrollController();
  bool _showQuickActions = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: widget.vsync,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();

    _scrollController.addListener(() {
      if (_scrollController.offset > 100 && _showQuickActions) {
        setState(() => _showQuickActions = false);
      } else if (_scrollController.offset <= 100 && !_showQuickActions) {
        setState(() => _showQuickActions = true);
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _refreshFeed(AppState appState) async {
    await appState.fetchPosts();
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final theme = Theme.of(context);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (appState.notifications.isNotEmpty &&
          appState.showInAppNotifications) {
        final notif = appState.notifications.last;
        if (!notif.isRead) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(notif.message,
                  style: const TextStyle(fontFamily: 'Tajawal')),
              backgroundColor: Colors.teal,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
            ),
          );
          appState.markNotificationAsRead(notif.id);
        }
      }
    });

    return Scaffold(
      floatingActionButton: FloatingActionButton(
        heroTag:
            'main_notification_fab_${DateTime.now().millisecondsSinceEpoch}',
        backgroundColor: Colors.teal,
        tooltip: 'إشعار تجريبي',
        child: const Icon(Icons.notifications_active),
        onPressed: () {
          appState.addNotification(
            NotificationModel(
              id: DateTime.now().toString(),
              message: 'هذا إشعار تجريبي داخل التطبيق',
              date: DateTime.now().toString(),
              isRead: false,
            ),
          );
        },
      ),
      body: RefreshIndicator(
        onRefresh: () => _refreshFeed(appState),
        color: theme.primaryColor,
        child: Stack(
          children: [
            AnimatedBackground(
              vsync: widget.vsync,
              behaviour: RandomParticleBehaviour(
                options: const ParticleOptions(
                  baseColor: Colors.teal,
                  spawnMinSpeed: 10.0,
                  spawnMaxSpeed: 30.0,
                  particleCount: 20,
                ),
              ),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            ZoomIn(
                                child: _buildPostCreationCard(appState, theme)),
                            const SizedBox(height: 16),
                            ZoomIn(
                                child: _buildStoriesCarousel(appState, theme)),
                          ],
                        ),
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: Text(
                          'آخر المنشورات',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.primaryColor,
                            fontFamily: 'Tajawal',
                          ),
                        ),
                      ),
                    ),
                    SliverPadding(
                      padding: const EdgeInsets.all(16.0),
                      sliver: SliverMasonryGrid.count(
                        crossAxisCount: 2,
                        mainAxisSpacing: 12,
                        crossAxisSpacing: 12,
                        childCount: appState.posts.length +
                            (appState.isLoadingMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == appState.posts.length) {
                            return Shimmer.fromColors(
                              baseColor: Colors.grey[300]!,
                              highlightColor: Colors.grey[100]!,
                              child: Container(
                                height: 200,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                ),
                              ),
                            );
                          }
                          final post = appState.posts[index];
                          return FadeInUp(
                            duration: const Duration(milliseconds: 300),
                            child: _buildPostCard(post, appState, theme, index),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (_showQuickActions)
              Positioned(
                bottom: 16,
                right: 16,
                child: ZoomIn(child: _buildQuickActionsBar(appState, theme)),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostCreationCard(AppState appState, ThemeData theme) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              decoration: InputDecoration(
                hintText: 'ما الذي تفكر به؟',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: theme.scaffoldBackgroundColor,
                hintStyle: const TextStyle(fontFamily: 'Tajawal'),
              ),
              maxLines: 3,
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  appState.addPost(value, null, null);
                }
              },
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ZoomIn(
                    child: _buildActionButton(
                  icon: Icons.image,
                  label: 'صورة',
                  onPressed: () async {
                    final picker = ImagePicker();
                    final image =
                        await picker.pickImage(source: ImageSource.gallery);
                    if (image != null) {
                      appState.addPost('منشور مع صورة', image.path, 'image');
                    }
                  },
                )),
                ZoomIn(
                    child: _buildActionButton(
                  icon: Icons.videocam,
                  label: 'فيديو',
                  onPressed: () async {
                    final connectivityResult =
                        await Connectivity().checkConnectivity();
                    if (connectivityResult == ConnectivityResult.none) {
                      appState.showSnackBar(
                          'لا يوجد اتصال بالإنترنت', Colors.red);
                      return;
                    }
                    final picker = ImagePicker();
                    final video =
                        await picker.pickVideo(source: ImageSource.gallery);
                    if (video != null) {
                      appState.addVideo('فيديو جديد', video.path);
                    }
                  },
                )),
                ZoomIn(
                    child: _buildActionButton(
                  icon: Icons.camera_alt,
                  label: 'قصة',
                  onPressed: () async {
                    final picker = ImagePicker();
                    final image =
                        await picker.pickImage(source: ImageSource.camera);
                    if (image != null) {
                      appState.addStory(image.path);
                    }
                  },
                )),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(icon, size: 20, color: Theme.of(context).primaryColor),
            const SizedBox(width: 4),
            Text(label,
                style: const TextStyle(fontSize: 14, fontFamily: 'Tajawal')),
          ],
        ),
      ),
    );
  }

  Widget _buildStoriesCarousel(AppState appState, ThemeData theme) {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: appState.stories.length + 1,
        itemBuilder: (context, index) {
          if (index == 0) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: GestureDetector(
                onTap: () async {
                  final picker = ImagePicker();
                  final image =
                      await picker.pickImage(source: ImageSource.camera);
                  if (image != null) {
                    await appState.addStory(image.path);
                    appState.showSnackBar('تم إضافة القصة بنجاح', Colors.green);
                  }
                },
                child: Container(
                  width: 80,
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: theme.primaryColor, width: 2),
                  ),
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.add_circle, size: 40, color: Colors.teal),
                      SizedBox(height: 4),
                      Text('إضافة قصة',
                          style:
                              TextStyle(fontSize: 12, fontFamily: 'Tajawal')),
                    ],
                  ),
                ),
              ),
            );
          }
          final story = appState.stories[index - 1];
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => StoryViewerScreen(story: story),
                  ),
                );
              },
              child: Hero(
                tag: 'story_${story.id}',
                child: Container(
                  width: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: theme.primaryColor, width: 2),
                    image: DecorationImage(
                      image: CachedNetworkImageProvider(story.image),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      color: Colors.black54,
                      child: Text(
                        story.user,
                        style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontFamily: 'Tajawal'),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPostCard(post, AppState appState, ThemeData theme, int index) {
    return Hero(
      tag: 'post_${post.id}',
      child: Card(
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundImage: post.profileImagePath != null
                        ? CachedNetworkImageProvider(post.profileImagePath!)
                        : null,
                    child: post.profileImagePath == null
                        ? const Icon(Icons.person)
                        : null,
                  ),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(post.username,
                          style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Tajawal')),
                      Text(post.date,
                          style: theme.textTheme.bodySmall
                              ?.copyWith(fontFamily: 'Tajawal')),
                    ],
                  ),
                  const Spacer(),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'delete') {
                        appState.deletePost(post.id);
                      } else if (value == 'edit') {
                        showDialog(
                          context: context,
                          builder: (context) {
                            String newContent = post.content;
                            return AlertDialog(
                              title: const Text('تعديل المنشور',
                                  style: TextStyle(fontFamily: 'Tajawal')),
                              content: TextField(
                                onChanged: (value) => newContent = value,
                                controller:
                                    TextEditingController(text: post.content),
                                decoration: const InputDecoration(
                                  hintText: 'أدخل النص الجديد',
                                  hintStyle: TextStyle(fontFamily: 'Tajawal'),
                                ),
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('إلغاء',
                                      style: TextStyle(fontFamily: 'Tajawal')),
                                ),
                                TextButton(
                                  onPressed: () {
                                    appState.editPost(post.id, newContent);
                                    Navigator.pop(context);
                                  },
                                  child: const Text('حفظ',
                                      style: TextStyle(fontFamily: 'Tajawal')),
                                ),
                              ],
                            );
                          },
                        );
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                          value: 'edit',
                          child: Text('تعديل',
                              style: TextStyle(fontFamily: 'Tajawal'))),
                      const PopupMenuItem(
                          value: 'delete',
                          child: Text('حذف',
                              style: TextStyle(fontFamily: 'Tajawal'))),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(post.content,
                  style: theme.textTheme.bodyMedium
                      ?.copyWith(fontFamily: 'Tajawal')),
              if (post.imageUrl != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedNetworkImage(
                      imageUrl: post.imageUrl!,
                      height: 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          height: 200,
                          color: Colors.white,
                        ),
                      ),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.error),
                    ),
                  ),
                ),
              if (post.videoUrl != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: VideoPlayerWidget(videoUrl: post.videoUrl!),
                ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  ZoomIn(
                      child: _ReactionButton(
                    icon: Icons.thumb_up,
                    label: '${post.reactions['like'] ?? 0}',
                    isActive: post.userReaction == 'like',
                    onPressed: () {
                      appState.reactToPost(post.id, 'like');
                      _showReactionAnimation(context, 'like', index);
                    },
                  )),
                  ZoomIn(
                      child: _ReactionButton(
                    icon: Icons.favorite,
                    label: '${post.reactions['love'] ?? 0}',
                    isActive: post.userReaction == 'love',
                    onPressed: () {
                      appState.reactToPost(post.id, 'love');
                      _showReactionAnimation(context, 'love', index);
                    },
                  )),
                  ZoomIn(
                      child: _ReactionButton(
                    icon: Icons.tag_faces,
                    label: '${post.reactions['haha'] ?? 0}',
                    isActive: post.userReaction == 'haha',
                    onPressed: () {
                      appState.reactToPost(post.id, 'haha');
                      _showReactionAnimation(context, 'haha', index);
                    },
                  )),
                ],
              ),
              const Divider(),
              TextButton(
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    builder: (context) {
                      String comment = '';
                      return StatefulBuilder(
                        builder: (context, setModalState) {
                          return Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                TextField(
                                  decoration: const InputDecoration(
                                    hintText: 'أضف تعليقًا...',
                                    border: OutlineInputBorder(),
                                    hintStyle: TextStyle(fontFamily: 'Tajawal'),
                                  ),
                                  onChanged: (value) =>
                                      setModalState(() => comment = value),
                                ),
                                const SizedBox(height: 8),
                                ElevatedButton(
                                  onPressed: () {
                                    if (comment.isNotEmpty) {
                                      appState.addCommentToPost(
                                          post.id, comment);
                                      Navigator.pop(context);
                                    }
                                  },
                                  child: const Text('نشر التعليق',
                                      style: TextStyle(fontFamily: 'Tajawal')),
                                ),
                                Expanded(
                                  child: ListView.builder(
                                    itemCount: post.comments.length,
                                    itemBuilder: (context, index) {
                                      final comment = post.comments[index];
                                      return ListTile(
                                        leading: const Icon(Icons.comment),
                                        title: Text(comment.username,
                                            style: const TextStyle(
                                                fontFamily: 'Tajawal')),
                                        subtitle: Text(comment.content,
                                            style: const TextStyle(
                                                fontFamily: 'Tajawal')),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  );
                },
                child: Text('عرض التعليقات (${post.comments.length})',
                    style: const TextStyle(fontFamily: 'Tajawal')),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showReactionAnimation(
      BuildContext context, String reaction, int index) {
    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    OverlayEntry? overlayEntry;
    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx + 50,
        top: position.dy - 50,
        child: AnimatedOpacity(
          opacity: 1.0,
          duration: const Duration(milliseconds: 500),
          child: Lottie.asset(
            'assets/animations/$reaction.json',
            height: 50,
            width: 50,
            repeat: false,
            onLoaded: (composition) {
              Future.delayed(const Duration(milliseconds: 1000), () {
                overlayEntry?.remove();
              });
            },
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);
  }

  Widget _buildQuickActionsBar(AppState appState, ThemeData theme) {
    return AnimatedOpacity(
      opacity: _showQuickActions ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[500]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ZoomIn(
              child: _buildQuickActionButton(
                icon: Icons.search,
                label: 'بحث',
                onPressed: () {
                  Navigator.pushNamed(context, '/search');
                },
              ),
            ),
            const SizedBox(width: 8),
            ZoomIn(
              child: _buildQuickActionButton(
                icon: Icons.map,
                label: 'الخريطة',
                onPressed: () {
                  Navigator.pushNamed(context, '/map');
                },
              ),
            ),
            const SizedBox(width: 8),
            ZoomIn(
              child: _buildQuickActionButton(
                icon: Icons.admin_panel_settings,
                label: 'لوحة التحكم',
                onPressed: () {
                  Navigator.pushNamed(context, '/admin');
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontFamily: 'Tajawal',
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ReactionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isActive;
  final VoidCallback onPressed;

  const _ReactionButton({
    required this.icon,
    required this.label,
    required this.isActive,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isActive
              ? Theme.of(context).primaryColor.withOpacity(0.2)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isActive ? Theme.of(context).primaryColor : Colors.grey,
              size: 20,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: isActive ? Theme.of(context).primaryColor : Colors.grey,
                fontFamily: 'Tajawal',
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class StoryViewerScreen extends StatefulWidget {
  final Story story;

  const StoryViewerScreen({super.key, required this.story});

  @override
  _StoryViewerScreenState createState() => _StoryViewerScreenState();
}

class _StoryViewerScreenState extends State<StoryViewerScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    );
    _progressAnimation = Tween<double>(begin: 0, end: 1).animate(_controller);
    _controller.forward().then((_) {
      Navigator.pop(context);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Hero(
            tag: 'story_${widget.story.id}',
            child: CachedNetworkImage(
              imageUrl: widget.story.image,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
              placeholder: (context, url) => const Center(
                child: CircularProgressIndicator(),
              ),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
          ),
          Positioned(
            top: 40,
            left: 16,
            right: 16,
            child: Row(
              children: [
                CircleAvatar(
                  backgroundImage:
                      CachedNetworkImageProvider(widget.story.user),
                ),
                const SizedBox(width: 8),
                Text(
                  widget.story.user,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Tajawal',
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          Positioned(
            top: 20,
            left: 16,
            right: 16,
            child: LinearProgressIndicator(
              value: _progressAnimation.value,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.teal),
            ),
          ),
        ],
      ),
    );
  }
}

class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;

  const VideoPlayerWidget({super.key, required this.videoUrl});

  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.network(widget.videoUrl)
      ..initialize().then((_) {
        setState(() {
          _isInitialized = true;
        });
        _controller.play();
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _isInitialized
        ? AspectRatio(
            aspectRatio: _controller.value.aspectRatio,
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                VideoPlayer(_controller),
                IconButton(
                  icon: Icon(
                    _controller.value.isPlaying
                        ? Icons.pause
                        : Icons.play_arrow,
                    color: Colors.white,
                    size: 40,
                  ),
                  onPressed: () {
                    setState(() {
                      if (_controller.value.isPlaying) {
                        _controller.pause();
                      } else {
                        _controller.play();
                      }
                    });
                  },
                ),
              ],
            ),
          )
        : const Center(child: CircularProgressIndicator());
  }
}
